import type { Persona } from '@/types/portfolio';

export const portfolioData: Record<Persona['id'], Persona> = {
  'engineer': {
    id: 'engineer',
    title: 'IT Operations Specialist',
    emoji: '💻',
    color: 'rgba(99, 102, 241, 1)',
    description: 'Who builds and maintains robust IT infrastructure with expertise in system administration, cybersecurity, and service management',
    inspirations: [
      {
        name: '<PERSON><PERSON><PERSON>',
        role: 'CEO of Microsoft',
        image: '/inspirations/<PERSON>tya-<PERSON>della.jpg',
        lessons: [
          'Transform organizational culture through empathy and inclusive leadership',
          'Drive digital transformation while maintaining focus on human empowerment',
          'Build cloud-first, mobile-first technology solutions that scale globally',
          'Foster continuous learning and growth mindset in technology teams'
        ]
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        role: 'Former CEO of IBM',
        image: '/inspirations/Ginni-<PERSON>.jpg',
        lessons: [
         'Champion diversity and inclusion in technology leadership roles',
         'Navigate complex enterprise transformations with strategic vision',
         'Integrate emerging technologies like AI into traditional business models',
         'Advocate for responsible technology development and ethical AI practices'
        ]
      },
      {
        name: '<PERSON><PERSON>',
        role: 'CEO of Alphabet and Google',
        image: '/inspirations/Sundar-Pichai.jpg',
        lessons: [
          'Lead with humility while managing complex global technology platforms',
          'Balance innovation with responsibility in AI and technology development',
          'Create accessible technology solutions that serve diverse global communities',
          'Foster collaborative engineering culture that prioritizes user experience'
        ]
      },
      {
        name: 'Susan Wojcicki',
        role: 'Former CEO of YouTube',
        image: '/inspirations/Susan-Wojcicki.jpg',
        lessons: [
          'Build scalable platforms that connect and empower global communities',
          'Navigate complex content and technology challenges with principled leadership',
          'Champion women in technology and create inclusive workplace cultures',
          'Balance rapid growth with platform responsibility and user safety'
        ]
      }
    ],
    experiences: [
      {
        title: '🎓 IT Operations Diploma',
        description: 'Currently pursuing comprehensive IT Operations training at Red River College, focusing on system administration, cybersecurity, networking, and ITIL service management. Gaining hands-on experience with VMware/VirtualBox virtualization, Windows/Linux/iOS administration, PowerShell scripting, Cisco networking, and security implementation including SIEM tools and incident response procedures.',
        date: 'August 2024 - Present (Graduates 2026)',
        tags: ['System Administration', 'Cybersecurity', 'ITIL Framework', 'Network Configuration', 'Virtualization'],
        link: ''
      },
      {
        title: '🔐 Cybersecurity & SIEM Implementation',
        description: 'Developed expertise in threat analysis, identifying malware, phishing, DDoS attacks, and insider threats. Implemented security controls including encryption, access controls, and endpoint protection. Gained experience with SIEM tools for monitoring and analyzing security events, following structured incident response procedures for containment, eradication, and recovery.',
        date: '2024 - Present',
        tags: ['Threat Analysis', 'SIEM Tools', 'Incident Response', 'Security Controls', 'Compliance'],
        link: ''
      },
      {
        title: '🌐 Network Infrastructure & Cloud Services',
        description: 'Configured wired/wireless networks, VPNs, and VLANs using Cisco Packet Tracer. Implemented TCP/IP and DNS troubleshooting, firewall configuration, and network segmentation. Gained understanding of cloud computing concepts including OneDrive, IoT environments, and hybrid cloud architectures with monitoring tools like Nagios.',
        date: '2024 - Present',
        tags: ['Network Configuration', 'Cisco Packet Tracer', 'Cloud Computing', 'VPN/VLAN', 'Monitoring Tools'],
        link: ''
      },
      {
        title: '🛠️ ITIL Service Management & Help Desk Operations',
        description: 'Mastered ITIL framework including service lifecycle management, incident management, problem management, and change management. Experienced with ticketing systems for incident tracking, SLA management, and knowledge base maintenance. Developed skills in customer support, de-escalation techniques, and creating service documentation.',
        date: '2024 - Present',
        tags: ['ITIL Framework', 'Service Management', 'Ticketing Systems', 'SLA Management', 'Documentation'],
        link: ''
      },
      {
        title: '💾 Database Management & Programming',
        description: 'Developed SQL proficiency for relational database queries and data management. Gained programming experience in Python (variables, functions, control flow, error handling) and web development with HTML/CSS. Created PowerShell scripts for system automation and task scheduling.',
        date: '2024 - Present',
        tags: ['SQL Database', 'Python Programming', 'PowerShell Scripting', 'Web Development', 'Automation'],
        link: ''
      },
      {
        title: '📋 Quality Assurance & Testing Methodologies',
        description: 'Learned comprehensive testing approaches including black box, white box, and gray box testing. Developed skills in test planning, creating quality metrics, defect management, and understanding UI automation frameworks. Focused on identifying, documenting, and prioritizing software issues.',
        date: '2024 - Present',
        tags: ['Quality Assurance', 'Test Planning', 'Defect Management', 'Testing Methodologies', 'UI Automation'],
        link: ''
      },
      {
        title: '🏆 Professional Certifications & Training',
        description: 'Completed International Certification of Digital Literacy (ICDL), Python Essential Training, HTML & CSS Web Development, and Quality Assurance Software Testing. Gained expertise in Adobe XD for design, AI productivity tools, and business language certification (CLB 8) for professional communication.',
        date: '2024 - Present',
        tags: ['ICDL Certification', 'Professional Training', 'Digital Literacy', 'Technical Skills', 'Communication'],
        link: ''
      }
    ]
  },
  'educator': {
    id: 'educator',
    title: 'Customer Service Professional',
    emoji: '🤝',
    color: 'rgba(139, 92, 246, 1)',
    description: 'Who delivers exceptional customer experiences through professional communication, problem-solving, and relationship management',
    inspirations: [
      {
        name: 'Tony Hsieh',
        role: 'Former CEO of Zappos',
        image: '/inspirations/Tony-Hsieh.jpg',
        lessons: [
          'Build company culture around delivering exceptional customer service experiences',
          'Empower employees to go above and beyond for customer satisfaction',
          'Create systems that prioritize customer happiness over short-term profits',
          'Foster authentic relationships and emotional connections with customers'
        ]
      },
      {
        name: 'Marc Benioff',
        role: 'CEO of Salesforce',
        image: '/inspirations/Marc-Benioff.jpg',
        lessons: [
          'Champion customer success as the foundation of business growth',
          'Leverage technology to enhance rather than replace human customer interactions',
          'Build scalable customer relationship management systems',
          'Maintain focus on customer trust and data privacy in service delivery'
        ]
      },
      {
        name: 'Shep Hyken',
        role: 'Customer Service Expert & Author',
        image: '/inspirations/Shep-Hyken.jpg',
        lessons: [
              'Transform customer service from cost center to competitive advantage',
              'Create consistent, memorable customer experiences across all touchpoints',
              'Develop customer service skills that build long-term loyalty',
              'Use customer feedback to drive continuous service improvement'
        ]
      }
    ],
    experiences: [
      {
        title: '🛍️ Store Associate - Marshalls/Home Sense',
        description: 'Delivering exceptional customer service excellence through proactive customer engagement, personalized shopping assistance, and professional problem resolution. Trained in company brand standards, service scripts, and conflict de-escalation techniques. Successfully manage customer complaints, returns, and exchanges while maintaining positive relationships and building customer loyalty.',
        date: 'June 2025 - Present',
        tags: ['Customer Service Excellence', 'Problem Resolution', 'Brand Standards', 'Relationship Management'],
        link: ''
      },
      {
        title: '🤝 Cross-Cultural Customer Communication',
        description: 'Developed expertise in serving diverse customer base through active listening, cultural competence, and professional communication. Effectively communicate with customers from various cultural backgrounds, understand customer needs and preferences, and provide appropriate product recommendations that enhance the shopping experience.',
        date: 'June 2025 - Present',
        tags: ['Cross-Cultural Competence', 'Active Listening', 'Professional Communication', 'Customer Engagement'],
        link: ''
      },
      {
        title: '💼 Business Language Certification (CLB 8)',
        description: 'Completed comprehensive business communication training at Red River College, enhancing professional communication skills vital for client interactions, technical documentation, and cross-team collaboration. Developed conflict resolution skills essential for managing stakeholder disagreements and customer complaints in professional environments.',
        date: 'January 2024 - April 2024',
        tags: ['Business Communication', 'Conflict Resolution', 'Professional Development', 'Team Collaboration'],
        link: ''
      },
      {
        title: '🎯 Customer Experience & Service Design',
        description: 'Applied customer experience principles through creating customer journey maps, stakeholder matrices, and service improvements. Focused on understanding business processes and aligning service delivery with organizational goals while maintaining high standards of customer satisfaction and professional service excellence.',
        date: '2024 - Present',
        tags: ['Customer Experience', 'Service Design', 'Process Improvement', 'Stakeholder Management'],
        link: ''
      },
      {
        title: '🏆 Health & Safety Committee Member',
        description: 'Voluntarily joined workplace Health & Safety Committee, developing risk assessment skills, compliance understanding, and safety protocols. These experiences directly translate to understanding security practices, risk management, and maintaining safe, professional work environments that prioritize both employee and customer wellbeing.',
        date: '2025 - Present',
        tags: ['Risk Assessment', 'Compliance', 'Safety Protocols', 'Team Leadership'],
        link: ''
      }
    ]
  },
  'movement-builder': {
    id: 'movement-builder',
    title: 'Creative & Project Coordinator',
    emoji: '🎨',
    color: 'rgba(236, 72, 153, 1)',
    description: 'Who combines creative design expertise with project management skills to deliver engaging user experiences and successful project outcomes',
    inspirations: [
      {
        name: 'Paula Scher',
        role: 'Graphic Designer & Partner at Pentagram',
        image: '/inspirations/Paula-Scher.jpg',
        lessons: [
          'Create bold, memorable visual identities that communicate brand essence effectively',
          'Balance artistic creativity with strategic business communication needs',
          'Develop design solutions that are both aesthetically pleasing and functionally effective',
          'Lead creative teams while maintaining high standards of design excellence'
        ]
      },
      {
        name: 'John Maeda',
        role: 'Former President of RISD, Design & Technology Leader',
        image: '/inspirations/John-Maeda.jpg',
        lessons: [
            'Bridge the gap between technology and creative design thinking',
            'Advocate for design as a critical component of business strategy',
            'Combine analytical thinking with creative problem-solving approaches',
            'Foster inclusive design practices that serve diverse user communities'
        ]
      },
      {
        name: 'Julie Zhuo',
        role: 'Former VP of Product Design at Facebook',
        image: '/inspirations/Julie-Zhuo.jpg',
        lessons: [
            'Scale design processes and teams while maintaining quality and consistency',
            'Integrate user research and data insights into creative design decisions',
            'Build design systems that enable both creativity and operational efficiency',
            'Lead cross-functional teams to deliver user-centered product experiences'
        ]
      },
      {
        name: 'Brené Brown',
        role: 'Research Professor & Leadership Expert',
        image: '/inspirations/Brene-Brown.jpg',
        lessons: [
            'Lead creative projects with vulnerability, courage, and authentic communication',
            'Build trust and psychological safety in collaborative creative environments',
            'Embrace failure as an essential component of creative innovation',
            'Foster inclusive team cultures that encourage diverse creative perspectives'
        ]
      }
    ],
    experiences: [
      {
        title: '🎨 Graphic Design Volunteer - Red River College',
        description: 'Delivered creative design solutions for college marketing and communication needs, demonstrating technical adaptability and software learning capability. Managed multiple design projects simultaneously while meeting tight deadlines, showing strong project management skills crucial for IT project delivery. Collaborated with clients to understand requirements and translate needs into effective visual deliverables.',
        date: 'January 2024 - April 2024',
        tags: ['Graphic Design', 'Project Management', 'Client Consultation', 'Creative Software'],
        link: ''
      },
      {
        title: '🌟 Promotions Coordinator (Volunteer)',
        description: 'Managing multi-platform social media campaigns and digital marketing strategies, creating engaging video content and maintaining consistent brand identity across channels. Developing content creation skills relevant for technical tutorials, system demonstrations, and training materials. Applying strategic thinking about engagement and reach to user adoption strategies.',
        date: 'May 2025 - Present',
        tags: ['Social Media Management', 'Content Creation', 'Digital Marketing', 'Brand Management'],
        link: ''
      },
      {
        title: '🎭 Cultural Event Coordination - Henna Art Volunteer',
        description: 'Coordinated and participated in cultural events at Red River College and Artbeat Studio, demonstrating attention to detail through intricate pattern creation and public engagement skills. Built confidence for user training and technical presentations while managing event logistics and direct interaction with diverse audiences.',
        date: 'April 2024 - June 2024',
        tags: ['Event Coordination', 'Cultural Engagement', 'Public Speaking', 'Attention to Detail'],
        link: ''
      },
      {
        title: '🎉 Joy Project Committee Member',
        description: 'Participated in workplace Joy Project initiative, demonstrating event planning and execution experience that showcases project management, team collaboration, and stakeholder engagement skills essential for IT project delivery. Contributed to creating positive workplace culture and employee engagement initiatives.',
        date: '2025 - Present',
        tags: ['Event Planning', 'Team Collaboration', 'Project Management', 'Stakeholder Engagement'],
        link: ''
      },
      {
        title: '🎓 Creative & Technical Skills Development',
        description: 'Developed proficiency in design applications including MediBang Paint, Photoshop, and Canva for creating professional documentation and presentations. Completed Adobe XD training for user experience design, demonstrating ability to bridge creative design with technical implementation and user-centered thinking.',
        date: '2024 - Present',
        tags: ['Adobe Creative Suite', 'UX Design', 'Visual Communication', 'Technical Documentation'],
        link: ''
      },
      {
        title: '📚 Academic Excellence - Mathematics & Physics',
        description: 'Graduated with high GPA (4.78) from Imamreza High School with specialization in Mathematics and Physics, demonstrating strong analytical thinking and problem-solving skills. This STEM foundation provides crucial background for programming, algorithm development, and logical system design approaches.',
        date: 'September 2019 - June 2022',
        tags: ['Academic Excellence', 'STEM Foundation', 'Problem Solving', 'Analytical Thinking'],
        link: ''
      }
    ]
  }
}; 
