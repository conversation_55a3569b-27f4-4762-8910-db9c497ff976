import { Card } from '@/components/ui/card';
import type { PersonaId } from '@/types/portfolio';

interface MainBubbleBioProps {
  onSelectPersona?: (id: PersonaId) => void;
}

export function MainBubbleBio({ onSelectPersona }: MainBubbleBioProps) {
  const scrollToPersonas = () => {
    document.getElementById('personas')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handlePersonaClick = (id: PersonaId) => {
    if (onSelectPersona) {
      onSelectPersona(id);
    }
  };

  return (
    <div 
      id="persona-details"
      className="mt-12"
    >
      <Card className="p-8 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20">
        <div className="flex flex-col items-center text-center space-y-6">
          <h2 className="text-3xl font-bold">👋 Hi, I&apos;m Ghazal</h2>
          <div className="text-lg text-muted-foreground max-w-2xl space-y-4">
            <p>
              I&apos;m a dynamic professional combining{' '}
              <button
                onClick={() => handlePersonaClick('engineer')}
                className="text-[#6366F1] bg-[#6366F1]/10 px-1 rounded hover:bg-[#6366F1]/20 transition-colors"
              >
                IT operations expertise
              </button>,{' '}
              <button
                onClick={() => handlePersonaClick('educator')}
                className="text-[#8B5CF6] bg-[#8B5CF6]/10 px-1 rounded hover:bg-[#8B5CF6]/20 transition-colors"
              >
                customer service excellence
              </button>, and{' '}
              <button
                onClick={() => handlePersonaClick('movement-builder')}
                className="text-[#EC4899] bg-[#EC4899]/10 px-1 rounded hover:bg-[#EC4899]/20 transition-colors"
              >
                creative project coordination
              </button>{' '}
              - three complementary skill sets that create a unique foundation for technology-driven business solutions.
            </p>
            <p>
            Currently pursuing my IT Operations Diploma at Red River College, I bring hands-on experience in system administration, cybersecurity, and ITIL service management, combined with proven customer service excellence from my retail experience.            </p>
            <p>
            My creative background in graphic design and project coordination allows me to bridge technical solutions with user experience, ensuring technology implementations are both functional and accessible.            </p>
            <p>
              <strong>This <span className="animate-shimmer bg-[linear-gradient(110deg,#fff,15%,#6366F1,35%,#8B5CF6,50%,#EC4899,65%,#fff,85%,#fff)] bg-[length:200%_100%] [-webkit-background-clip:text] [-webkit-text-fill-color:transparent]">combination</span> enables me to deliver comprehensive IT solutions that prioritize both technical excellence and exceptional user experience.</strong>
            </p>
          </div>
          <button
            onClick={scrollToPersonas}
            className="text-muted-foreground text-base hover:text-accent1 transition-colors cursor-pointer animate-pulse mt-4"
          >
            ✨ Click on the IT Operations Specialist, Customer Service Professional, or Creative & Project Coordinator bubbles to explore how each persona contributes to my technology solutions approach
          </button>
        </div>
      </Card>
    </div>
  );
} 