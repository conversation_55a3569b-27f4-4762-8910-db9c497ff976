import type { DevRelPillar } from '@/types/portfolio';

export const pillarData: DevRelPillar[] = [
  {
    id: 'healthcare-expertise',
    title: 'Healthcare Research',
    description: 'Scientific background and research experience in pharmaceutical development, quality assurance, and healthcare systems.',
    icon: '🧪',
    color: 'from-accent1/20 to-accent1/10'
  },
  {
    id: 'business-strategy',
    title: 'Business Strategy',
    description: 'MBA education from Tuck with focus on healthcare market analysis, commercialization strategies, and business development.',
    icon: '📊',
    color: 'from-accent2/20 to-accent2/10'
  },
  {
    id: 'ai-systems',
    title: 'AI Systems Development',
    description: 'Creating sophisticated AI architectures for healthcare applications with focus on security, clinical workflows, and patient outcomes.',
    icon: '👨‍💻',
    color: 'from-accent2/20 to-accent2/10'
  },
  {
    id: 'data-analytics',
    title: 'Data Analytics',
    description: 'Leveraging data analysis and visualization techniques to drive process improvements and strategic decision-making in healthcare.',
    icon: '📈',
    color: 'from-accent1/20 to-accent2/10'
  },
  {
    id: 'healthcare-innovation',
    title: 'Healthcare Innovation',
    description: 'Bridging science, business, and technology to transform healthcare delivery systems and improve patient outcomes.',
    icon: '🏥',
    color: 'from-accent2/20 to-accent3/10'
  },
  {
    id: 'process-optimization',
    title: 'Process Optimization',
    description: 'Applying Lean Six Sigma principles and quality management methodologies to enhance efficiency and reduce costs.',
    icon: '⚙️',
    color: 'from-accent2/20 to-accent3/10'
  }
]; 